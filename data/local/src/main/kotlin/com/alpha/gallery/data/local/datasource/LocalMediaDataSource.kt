package com.alpha.gallery.data.local.datasource

import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.database.dao.MediaItemDao
import com.alpha.gallery.core.database.mapper.toDomainModel
import com.alpha.gallery.core.database.mapper.toDomainModels
import com.alpha.gallery.core.domain.model.Album
import com.alpha.gallery.core.domain.model.MediaItem
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Local data source for media items and albums
 * Provides access to locally stored media data from Room database
 */
@Singleton
class LocalMediaDataSource @Inject constructor(
    private val mediaItemDao: MediaItemDao,
    private val albumDao: AlbumDao
) {
    
    /**
     * Get all media items as Flow
     */
    fun getAllMediaItems(): Flow<List<MediaItem>> {
        return mediaItemDao.getAllMediaItems().map { entities ->
            entities.toDomainModels()
        }
    }
    
    /**
     * Get media items by album ID
     */
    fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItem>> {
        return mediaItemDao.getMediaItemsByAlbum(albumId).map { entities ->
            entities.toDomainModels()
        }
    }
    
    /**
     * Get media item by ID
     */
    suspend fun getMediaItemById(id: String): MediaItem? {
        val mediaStoreId = id.toLongOrNull() ?: return null
        return mediaItemDao.getMediaItemByMediaStoreId(mediaStoreId)?.toDomainModel()
    }
    
    /**
     * Get all albums as Flow
     */
    fun getAllAlbums(): Flow<List<Album>> {
        return albumDao.getAllAlbums().map { entities ->
            entities.toDomainModels()
        }
    }
    
    /**
     * Get album by ID
     */
    suspend fun getAlbumById(id: String): Album? {
        return albumDao.getAlbumByBucketId(id)?.toDomainModel()
    }
    
    /**
     * Search media items by query
     */
    fun searchMediaItems(query: String): Flow<List<MediaItem>> {
        return mediaItemDao.searchMediaItems(query).map { entities ->
            entities.toDomainModels()
        }
    }
    
    /**
     * Get recent media items
     */
    fun getRecentMediaItems(limit: Int): Flow<List<MediaItem>> {
        return mediaItemDao.getRecentMediaItems(limit).map { entities ->
            entities.toDomainModels()
        }
    }
    
    /**
     * Get favorite media items
     */
    fun getFavoriteMediaItems(): Flow<List<MediaItem>> {
        return mediaItemDao.getFavoriteMediaItems().map { entities ->
            entities.toDomainModels()
        }
    }
    
    /**
     * Add media item to favorites
     */
    suspend fun addToFavorites(mediaItemId: String) {
        val mediaStoreId = mediaItemId.toLongOrNull() ?: return
        mediaItemDao.markAsFavorite(mediaStoreId)
    }
    
    /**
     * Remove media item from favorites
     */
    suspend fun removeFromFavorites(mediaItemId: String) {
        val mediaStoreId = mediaItemId.toLongOrNull() ?: return
        mediaItemDao.removeFromFavorites(mediaStoreId)
    }
    
    /**
     * Delete media item (soft delete - mark as trashed)
     */
    suspend fun deleteMediaItem(mediaItemId: String): Boolean {
        return try {
            val mediaStoreId = mediaItemId.toLongOrNull() ?: return false
            mediaItemDao.markAsTrashed(mediaStoreId)
            true
        } catch (e: Exception) {
            false
        }
    }
}
