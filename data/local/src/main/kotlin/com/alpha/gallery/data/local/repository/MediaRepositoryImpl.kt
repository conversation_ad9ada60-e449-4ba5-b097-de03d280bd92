package com.alpha.gallery.data.local.repository

import com.alpha.gallery.core.domain.model.Album
import com.alpha.gallery.core.domain.model.MediaItem
import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.core.sync.scheduler.SyncScheduler
import com.alpha.gallery.data.local.datasource.LocalMediaDataSource
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of MediaRepository that coordinates local data access and synchronization
 */
@Singleton
class MediaRepositoryImpl @Inject constructor(
    private val localDataSource: LocalMediaDataSource,
    private val syncScheduler: SyncScheduler
) : MediaRepository {
    
    override fun getAllMediaItems(): Flow<List<MediaItem>> {
        return localDataSource.getAllMediaItems()
    }
    
    override fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItem>> {
        return localDataSource.getMediaItemsByAlbum(albumId)
    }
    
    override suspend fun getMediaItemById(id: String): MediaItem? {
        return localDataSource.getMediaItemById(id)
    }
    
    override fun getAllAlbums(): Flow<List<Album>> {
        return localDataSource.getAllAlbums()
    }
    
    override suspend fun getAlbumById(id: String): Album? {
        return localDataSource.getAlbumById(id)
    }
    
    override fun searchMediaItems(query: String): Flow<List<MediaItem>> {
        return localDataSource.searchMediaItems(query)
    }
    
    override fun getRecentMediaItems(limit: Int): Flow<List<MediaItem>> {
        return localDataSource.getRecentMediaItems(limit)
    }
    
    override fun getFavoriteMediaItems(): Flow<List<MediaItem>> {
        return localDataSource.getFavoriteMediaItems()
    }
    
    override suspend fun addToFavorites(mediaItemId: String) {
        localDataSource.addToFavorites(mediaItemId)
    }
    
    override suspend fun removeFromFavorites(mediaItemId: String) {
        localDataSource.removeFromFavorites(mediaItemId)
    }
    
    override suspend fun deleteMediaItem(mediaItemId: String): Boolean {
        return localDataSource.deleteMediaItem(mediaItemId)
    }
    
    override suspend fun refreshMediaItems() {
        // Trigger immediate sync to refresh media items from device storage
        syncScheduler.triggerImmediateSync(forceFullSync = false)
    }
    
    override suspend fun syncWithCloud() {
        // TODO: Implement cloud synchronization
        // This would coordinate with remote data source for cloud sync
    }
}
