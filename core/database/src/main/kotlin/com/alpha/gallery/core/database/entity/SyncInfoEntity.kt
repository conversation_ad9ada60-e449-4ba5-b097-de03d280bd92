package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Room entity for storing synchronization metadata
 * Tracks last sync timestamps and sync status for incremental synchronization
 */
@Entity(tableName = "sync_info")
data class SyncInfoEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "last_sync_timestamp")
    val lastSyncTimestamp: Long,
    
    @ColumnInfo(name = "last_full_sync_timestamp")
    val lastFullSyncTimestamp: Long = 0,
    
    @ColumnInfo(name = "sync_status")
    val syncStatus: String = SyncStatus.IDLE.name,
    
    @ColumnInfo(name = "last_error_message")
    val lastErrorMessage: String? = null,
    
    @ColumnInfo(name = "total_media_count")
    val totalMediaCount: Int = 0,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
) {
    companion object {
        const val MEDIA_SYNC_ID = "media_sync"
        const val ALBUM_SYNC_ID = "album_sync"
    }
}

/**
 * Enum representing different sync states
 */
enum class SyncStatus {
    IDLE,
    SYNCING,
    SUCCESS,
    ERROR,
    CANCELLED
}
