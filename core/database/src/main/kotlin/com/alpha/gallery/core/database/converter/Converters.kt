package com.alpha.gallery.core.database.converter

import androidx.room.TypeConverter
import com.alpha.gallery.core.database.entity.SyncStatus

/**
 * Room type converters for custom data types
 */
class Converters {
    
    @TypeConverter
    fun fromSyncStatus(status: SyncStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toSyncStatus(status: String): SyncStatus {
        return try {
            SyncStatus.valueOf(status)
        } catch (e: IllegalArgumentException) {
            SyncStatus.IDLE
        }
    }
}
