package com.alpha.gallery.core.database.mapper

import com.alpha.gallery.core.database.entity.MediaItemEntity
import com.alpha.gallery.core.domain.model.MediaItem

/**
 * Mapper functions to convert between database entities and domain models
 */

/**
 * Convert MediaItemEntity to domain MediaItem
 */
fun MediaItemEntity.toDomainModel(): MediaItem {
    return MediaItem(
        id = mediaStoreId.toString(),
        name = displayName,
        path = relativePath ?: "",
        uri = uri,
        mimeType = mimeType,
        size = size,
        dateAdded = dateAdded * 1000, // Convert seconds to milliseconds
        dateModified = dateModified * 1000, // Convert seconds to milliseconds
        width = width,
        height = height,
        duration = duration,
        albumId = albumId,
        albumName = albumName,
        thumbnailPath = null, // Generated on demand
        isCloudItem = false,
        cloudUrl = null,
        isSynced = true
    )
}

/**
 * Convert list of MediaItemEntity to list of domain MediaItem
 */
fun List<MediaItemEntity>.toDomainModels(): List<MediaItem> {
    return map { it.toDomainModel() }
}

/**
 * Convert domain MediaItem to MediaItemEntity
 * Note: This is primarily for testing or manual insertion
 */
fun MediaItem.toEntity(): MediaItemEntity {
    return MediaItemEntity(
        mediaStoreId = id.toLongOrNull() ?: 0,
        uri = uri,
        displayName = name,
        mimeType = mimeType,
        size = size,
        dateAdded = dateAdded / 1000, // Convert milliseconds to seconds
        dateModified = dateModified / 1000, // Convert milliseconds to seconds
        width = width,
        height = height,
        duration = duration,
        albumId = albumId,
        albumName = albumName,
        relativePath = path.ifEmpty { null }
    )
}
