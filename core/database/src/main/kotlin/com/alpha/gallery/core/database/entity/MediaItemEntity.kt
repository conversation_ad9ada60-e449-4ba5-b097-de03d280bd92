package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Room entity for media items (images and videos)
 * Stores metadata from MediaStore for local database synchronization
 */
@Entity(
    tableName = "media_items",
    indices = [
        Index(value = ["media_store_id"], unique = true),
        Index(value = ["date_modified"]),
        Index(value = ["date_added"]),
        Index(value = ["album_id"]),
        Index(value = ["mime_type"])
    ]
)
data class MediaItemEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "media_store_id")
    val mediaStoreId: Long,
    
    @ColumnInfo(name = "uri")
    val uri: String,
    
    @ColumnInfo(name = "display_name")
    val displayName: String,
    
    @ColumnInfo(name = "mime_type")
    val mimeType: String,
    
    @ColumnInfo(name = "size")
    val size: Long,
    
    @ColumnInfo(name = "date_added")
    val dateAdded: Long, // MediaStore DATE_ADDED in seconds
    
    @ColumnInfo(name = "date_modified")
    val dateModified: Long, // MediaStore DATE_MODIFIED in seconds
    
    @ColumnInfo(name = "width")
    val width: Int = 0,
    
    @ColumnInfo(name = "height")
    val height: Int = 0,
    
    @ColumnInfo(name = "duration")
    val duration: Long = 0, // For videos, in milliseconds
    
    @ColumnInfo(name = "album_id")
    val albumId: String? = null,
    
    @ColumnInfo(name = "album_name")
    val albumName: String? = null,
    
    @ColumnInfo(name = "relative_path")
    val relativePath: String? = null, // MediaStore RELATIVE_PATH
    
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean = false,
    
    @ColumnInfo(name = "is_trashed")
    val isTrashed: Boolean = false,
    
    @ColumnInfo(name = "last_sync_timestamp")
    val lastSyncTimestamp: Long = System.currentTimeMillis()
) {
    val isVideo: Boolean
        get() = mimeType.startsWith("video/")
    
    val isImage: Boolean
        get() = mimeType.startsWith("image/")
}
