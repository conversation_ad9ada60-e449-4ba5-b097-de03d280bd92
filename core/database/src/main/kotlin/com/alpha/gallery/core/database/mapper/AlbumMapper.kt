package com.alpha.gallery.core.database.mapper

import com.alpha.gallery.core.database.entity.AlbumEntity
import com.alpha.gallery.core.domain.model.Album

/**
 * Mapper functions to convert between album database entities and domain models
 */

/**
 * Convert AlbumEntity to domain Album
 */
fun AlbumEntity.toDomainModel(): Album {
    return Album(
        id = bucketId,
        name = bucketDisplayName,
        path = relativePath,
        coverImagePath = null, // Generated on demand from coverMediaId
        mediaCount = mediaCount,
        dateAdded = dateAdded * 1000, // Convert seconds to milliseconds
        dateModified = dateModified * 1000, // Convert seconds to milliseconds
        isCloudAlbum = false
    )
}

/**
 * Convert list of AlbumEntity to list of domain Album
 */
fun List<AlbumEntity>.toDomainModels(): List<Album> {
    return map { it.toDomainModel() }
}

/**
 * Convert domain Album to AlbumEntity
 * Note: This is primarily for testing or manual insertion
 */
fun Album.toEntity(): AlbumEntity {
    return AlbumEntity(
        bucketId = id,
        bucketDisplayName = name,
        relativePath = path,
        mediaCount = mediaCount,
        dateAdded = dateAdded / 1000, // Convert milliseconds to seconds
        dateModified = dateModified / 1000 // Convert milliseconds to seconds
    )
}
