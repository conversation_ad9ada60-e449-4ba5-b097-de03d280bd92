package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.AlbumEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for AlbumEntity operations
 * Provides database access methods for albums/folders
 */
@Dao
interface AlbumDao {
    
    /**
     * Get all albums ordered by date modified descending
     */
    @Query("SELECT * FROM albums ORDER BY date_modified DESC")
    fun getAllAlbums(): Flow<List<AlbumEntity>>
    
    /**
     * Get album by bucket ID
     */
    @Query("SELECT * FROM albums WHERE bucket_id = :bucketId LIMIT 1")
    suspend fun getAlbumByBucketId(bucketId: String): AlbumEntity?
    
    /**
     * Get album by internal ID
     */
    @Query("SELECT * FROM albums WHERE id = :id LIMIT 1")
    suspend fun getAlbumById(id: Long): AlbumEntity?
    
    /**
     * Get all bucket IDs for deletion detection
     */
    @Query("SELECT bucket_id FROM albums")
    suspend fun getAllBucketIds(): List<String>
    
    /**
     * Insert single album with conflict resolution
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlbum(album: AlbumEntity): Long
    
    /**
     * Insert multiple albums in batch
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlbums(albums: List<AlbumEntity>): List<Long>
    
    /**
     * Update existing album
     */
    @Update
    suspend fun updateAlbum(album: AlbumEntity)
    
    /**
     * Update multiple albums in batch
     */
    @Update
    suspend fun updateAlbums(albums: List<AlbumEntity>)
    
    /**
     * Delete albums by bucket IDs
     */
    @Query("DELETE FROM albums WHERE bucket_id IN (:bucketIds)")
    suspend fun deleteAlbumsByBucketIds(bucketIds: List<String>): Int
    
    /**
     * Update album media count
     */
    @Query("UPDATE albums SET media_count = :count WHERE bucket_id = :bucketId")
    suspend fun updateAlbumMediaCount(bucketId: String, count: Int)
    
    /**
     * Update album cover media ID
     */
    @Query("UPDATE albums SET cover_media_id = :coverMediaId WHERE bucket_id = :bucketId")
    suspend fun updateAlbumCover(bucketId: String, coverMediaId: Long?)
    
    /**
     * Get total count of albums
     */
    @Query("SELECT COUNT(*) FROM albums")
    suspend fun getTotalAlbumCount(): Int
    
    /**
     * Clear all albums (for full resync)
     */
    @Query("DELETE FROM albums")
    suspend fun clearAllAlbums()
}
