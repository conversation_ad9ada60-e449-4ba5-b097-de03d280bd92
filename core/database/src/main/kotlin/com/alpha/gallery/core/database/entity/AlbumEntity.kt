package com.alpha.gallery.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * Room entity for albums/folders
 * Represents collections of media items grouped by folder or album
 */
@Entity(
    tableName = "albums",
    indices = [
        Index(value = ["bucket_id"], unique = true),
        Index(value = ["date_modified"]),
        Index(value = ["relative_path"])
    ]
)
data class AlbumEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    @ColumnInfo(name = "bucket_id")
    val bucketId: String, // MediaStore BUCKET_ID
    
    @ColumnInfo(name = "bucket_display_name")
    val bucketDisplayName: String, // MediaStore BUCKET_DISPLAY_NAME
    
    @ColumnInfo(name = "relative_path")
    val relativePath: String, // MediaStore RELATIVE_PATH
    
    @ColumnInfo(name = "cover_media_id")
    val coverMediaId: Long? = null, // Reference to MediaItemEntity.mediaStoreId
    
    @ColumnInfo(name = "media_count")
    val mediaCount: Int = 0,
    
    @ColumnInfo(name = "date_added")
    val dateAdded: Long,
    
    @ColumnInfo(name = "date_modified")
    val dateModified: Long,
    
    @ColumnInfo(name = "last_sync_timestamp")
    val lastSyncTimestamp: Long = System.currentTimeMillis()
) {
    val displayName: String
        get() = if (bucketDisplayName.isBlank()) "Unknown Album" else bucketDisplayName
}
