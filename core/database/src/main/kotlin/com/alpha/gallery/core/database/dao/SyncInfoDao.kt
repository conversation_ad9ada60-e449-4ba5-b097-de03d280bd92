package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.SyncInfoEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for SyncInfoEntity operations
 * Manages synchronization metadata and state tracking
 */
@Dao
interface SyncInfoDao {
    
    /**
     * Get sync info by ID
     */
    @Query("SELECT * FROM sync_info WHERE id = :id LIMIT 1")
    suspend fun getSyncInfo(id: String): SyncInfoEntity?
    
    /**
     * Get sync info as Flow for reactive updates
     */
    @Query("SELECT * FROM sync_info WHERE id = :id LIMIT 1")
    fun getSyncInfoFlow(id: String): Flow<SyncInfoEntity?>
    
    /**
     * Get all sync info records
     */
    @Query("SELECT * FROM sync_info ORDER BY updated_at DESC")
    fun getAllSyncInfo(): Flow<List<SyncInfoEntity>>
    
    /**
     * Insert or update sync info
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateSyncInfo(syncInfo: SyncInfoEntity)
    
    /**
     * Update last sync timestamp
     */
    @Query("UPDATE sync_info SET last_sync_timestamp = :timestamp, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateLastSyncTimestamp(id: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update last full sync timestamp
     */
    @Query("UPDATE sync_info SET last_full_sync_timestamp = :timestamp, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateLastFullSyncTimestamp(id: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update sync status
     */
    @Query("UPDATE sync_info SET sync_status = :status, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateSyncStatus(id: String, status: String, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update sync status with error message
     */
    @Query("UPDATE sync_info SET sync_status = :status, last_error_message = :errorMessage, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateSyncStatusWithError(id: String, status: String, errorMessage: String?, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Update total media count
     */
    @Query("UPDATE sync_info SET total_media_count = :count, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateTotalMediaCount(id: String, count: Int, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * Delete sync info by ID
     */
    @Query("DELETE FROM sync_info WHERE id = :id")
    suspend fun deleteSyncInfo(id: String)
    
    /**
     * Clear all sync info (for reset)
     */
    @Query("DELETE FROM sync_info")
    suspend fun clearAllSyncInfo()
}
