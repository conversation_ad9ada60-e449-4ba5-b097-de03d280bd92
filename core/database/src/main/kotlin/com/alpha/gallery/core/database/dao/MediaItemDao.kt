package com.alpha.gallery.core.database.dao

import androidx.room.*
import com.alpha.gallery.core.database.entity.MediaItemEntity
import kotlinx.coroutines.flow.Flow

/**
 * DAO for MediaItemEntity operations
 * Provides database access methods for media items with batch processing support
 */
@Dao
interface MediaItemDao {
    
    /**
     * Get all media items ordered by date modified descending
     */
    @Query("SELECT * FROM media_items WHERE is_trashed = 0 ORDER BY date_modified DESC")
    fun getAllMediaItems(): Flow<List<MediaItemEntity>>
    
    /**
     * Get media items by album ID
     */
    @Query("SELECT * FROM media_items WHERE album_id = :albumId AND is_trashed = 0 ORDER BY date_modified DESC")
    fun getMediaItemsByAlbum(albumId: String): Flow<List<MediaItemEntity>>
    
    /**
     * Get media item by MediaStore ID
     */
    @Query("SELECT * FROM media_items WHERE media_store_id = :mediaStoreId LIMIT 1")
    suspend fun getMediaItemByMediaStoreId(mediaStoreId: Long): MediaItemEntity?
    
    /**
     * Get media item by internal ID
     */
    @Query("SELECT * FROM media_items WHERE id = :id LIMIT 1")
    suspend fun getMediaItemById(id: Long): MediaItemEntity?
    
    /**
     * Get all MediaStore IDs for deletion detection
     */
    @Query("SELECT media_store_id FROM media_items WHERE is_trashed = 0")
    suspend fun getAllMediaStoreIds(): List<Long>
    
    /**
     * Get recent media items with limit
     */
    @Query("SELECT * FROM media_items WHERE is_trashed = 0 ORDER BY date_added DESC LIMIT :limit")
    fun getRecentMediaItems(limit: Int): Flow<List<MediaItemEntity>>
    
    /**
     * Get favorite media items
     */
    @Query("SELECT * FROM media_items WHERE is_favorite = 1 AND is_trashed = 0 ORDER BY date_modified DESC")
    fun getFavoriteMediaItems(): Flow<List<MediaItemEntity>>
    
    /**
     * Search media items by display name
     */
    @Query("SELECT * FROM media_items WHERE display_name LIKE '%' || :query || '%' AND is_trashed = 0 ORDER BY date_modified DESC")
    fun searchMediaItems(query: String): Flow<List<MediaItemEntity>>
    
    /**
     * Insert single media item with conflict resolution
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaItem(mediaItem: MediaItemEntity): Long
    
    /**
     * Insert multiple media items in batch
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMediaItems(mediaItems: List<MediaItemEntity>): List<Long>
    
    /**
     * Update existing media item
     */
    @Update
    suspend fun updateMediaItem(mediaItem: MediaItemEntity)
    
    /**
     * Update multiple media items in batch
     */
    @Update
    suspend fun updateMediaItems(mediaItems: List<MediaItemEntity>)
    
    /**
     * Delete media items by MediaStore IDs
     */
    @Query("DELETE FROM media_items WHERE media_store_id IN (:mediaStoreIds)")
    suspend fun deleteMediaItemsByMediaStoreIds(mediaStoreIds: List<Long>): Int
    
    /**
     * Mark media item as favorite
     */
    @Query("UPDATE media_items SET is_favorite = 1 WHERE media_store_id = :mediaStoreId")
    suspend fun markAsFavorite(mediaStoreId: Long)
    
    /**
     * Remove from favorites
     */
    @Query("UPDATE media_items SET is_favorite = 0 WHERE media_store_id = :mediaStoreId")
    suspend fun removeFromFavorites(mediaStoreId: Long)
    
    /**
     * Mark media item as trashed (soft delete)
     */
    @Query("UPDATE media_items SET is_trashed = 1 WHERE media_store_id = :mediaStoreId")
    suspend fun markAsTrashed(mediaStoreId: Long)
    
    /**
     * Get maximum date modified for incremental sync
     */
    @Query("SELECT MAX(date_modified) FROM media_items")
    suspend fun getMaxDateModified(): Long?
    
    /**
     * Get total count of media items
     */
    @Query("SELECT COUNT(*) FROM media_items WHERE is_trashed = 0")
    suspend fun getTotalMediaCount(): Int
    
    /**
     * Clear all media items (for full resync)
     */
    @Query("DELETE FROM media_items")
    suspend fun clearAllMediaItems()
}
