package com.alpha.gallery.core.database.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.alpha.gallery.core.common.Constants
import com.alpha.gallery.core.database.converter.Converters
import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.database.dao.MediaItemDao
import com.alpha.gallery.core.database.dao.SyncInfoDao
import com.alpha.gallery.core.database.entity.AlbumEntity
import com.alpha.gallery.core.database.entity.MediaItemEntity
import com.alpha.gallery.core.database.entity.SyncInfoEntity

/**
 * Room database for Gallery app
 * Contains media items, albums, and sync information
 */
@Database(
    entities = [
        MediaItemEntity::class,
        AlbumEntity::class,
        SyncInfoEntity::class
    ],
    version = Constants.DATABASE_VERSION,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class GalleryDatabase : RoomDatabase() {
    
    abstract fun mediaItemDao(): MediaItemDao
    abstract fun albumDao(): AlbumDao
    abstract fun syncInfoDao(): SyncInfoDao
    
    companion object {
        @Volatile
        private var INSTANCE: GalleryDatabase? = null
        
        fun getDatabase(context: Context): GalleryDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    GalleryDatabase::class.java,
                    Constants.DATABASE_NAME
                )
                    .fallbackToDestructiveMigration() // For development - remove in production
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
