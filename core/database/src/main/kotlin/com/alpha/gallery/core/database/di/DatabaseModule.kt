package com.alpha.gallery.core.database.di

import android.content.Context
import androidx.room.Room
import com.alpha.gallery.core.common.Constants
import com.alpha.gallery.core.database.dao.AlbumDao
import com.alpha.gallery.core.database.dao.MediaItemDao
import com.alpha.gallery.core.database.dao.SyncInfoDao
import com.alpha.gallery.core.database.database.GalleryDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for database dependencies
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideGalleryDatabase(
        @ApplicationContext context: Context
    ): GalleryDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            GalleryDatabase::class.java,
            Constants.DATABASE_NAME
        )
            .fallbackToDestructiveMigration() // For development - remove in production
            .build()
    }
    
    @Provides
    fun provideMediaItemDao(database: GalleryDatabase): MediaItemDao {
        return database.mediaItemDao()
    }
    
    @Provides
    fun provideAlbumDao(database: GalleryDatabase): AlbumDao {
        return database.albumDao()
    }
    
    @Provides
    fun provideSyncInfoDao(database: GalleryDatabase): SyncInfoDao {
        return database.syncInfoDao()
    }
}
